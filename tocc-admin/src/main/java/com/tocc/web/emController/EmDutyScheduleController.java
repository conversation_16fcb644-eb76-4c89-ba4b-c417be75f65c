package com.tocc.web.emController;

import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.core.page.TableDataInfo;
import com.tocc.common.enums.BusinessType;
import com.tocc.common.utils.StringUtils;
import com.tocc.common.utils.bean.BeanUtils;
import com.tocc.common.utils.poi.ExcelUtil;
import com.tocc.duty.domain.dto.EmDutyPersonDTO;
import com.tocc.duty.domain.dto.EmDutyScheduleCreateDTO;
import com.tocc.duty.domain.dto.EmDutyScheduleDTO;
import com.tocc.duty.domain.vo.EmDutyPersonVO;
import com.tocc.duty.domain.vo.EmDutyScheduleDetailVO;
import com.tocc.duty.domain.vo.EmDutyScheduleVO;
import com.tocc.duty.service.IEmDutyScheduleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 值班安排Controller
 * 
 * <AUTHOR>
 */
@Api(tags = "值班安排管理")
@RestController
@RequestMapping("/duty/schedule")
@Validated
public class EmDutyScheduleController extends BaseController {
    
    @Autowired
    private IEmDutyScheduleService dutyScheduleService;
    
    /**
     * 查询值班安排列表
     */
    @ApiOperation("查询值班安排列表")
    @PreAuthorize("@ss.hasPermi('duty:schedule:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmDutyScheduleDTO dutySchedule) {
        startPage();
        List<EmDutyScheduleVO> list = dutyScheduleService.selectDutyScheduleList(dutySchedule);
        return getDataTable(list);
    }
    
    /**
     * 获取值班安排详细信息
     */
    @ApiOperation("获取值班安排详细信息")
    @PreAuthorize("@ss.hasPermi('duty:schedule:query')")
    @GetMapping("/{id}")
    public AjaxResult getInfo(
            @ApiParam(value = "值班安排ID", required = true)
            @PathVariable String id) {
        if (StringUtils.isEmpty(id)) {
            return error("值班安排ID不能为空");
        }
        EmDutyScheduleDetailVO detail = dutyScheduleService.selectDutyScheduleDetailById(id);
        if (detail == null) {
            return error("值班安排不存在");
        }
        return success(detail);
    }
    
    /**
     * 创建值班安排
     */
    @ApiOperation("创建值班安排")
    @PreAuthorize("@ss.hasPermi('duty:schedule:add')")
    @Log(title = "值班安排", businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public AjaxResult create(@Valid @RequestBody EmDutyScheduleCreateDTO dutyScheduleCreate) {
        try {
            int result = dutyScheduleService.createDutySchedule(dutyScheduleCreate);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("创建值班安排失败", e);
            return error("创建值班安排失败：" + e.getMessage());
        }
    }
    
    /**
     * 修改值班安排
     */
    @ApiOperation("修改值班安排")
    @PreAuthorize("@ss.hasPermi('duty:schedule:edit')")
    @Log(title = "值班安排", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public AjaxResult update(@Valid @RequestBody EmDutyScheduleDetailVO dutyScheduleDetail) {
        try {
            // 如果包含人员信息，使用包含人员的更新方法
            if (dutyScheduleDetail.getDutyPersons() != null && !dutyScheduleDetail.getDutyPersons().isEmpty()) {
                int result = dutyScheduleService.updateDutyScheduleWithPersons(dutyScheduleDetail);
                return toAjax(result);
            } else {
                // 否则只更新基本信息
                EmDutyScheduleDTO dutySchedule = new EmDutyScheduleDTO();
                BeanUtils.copyProperties(dutyScheduleDetail, dutySchedule);
                int result = dutyScheduleService.updateDutySchedule(dutySchedule);
                return toAjax(result);
            }
        } catch (Exception e) {
            logger.error("修改值班安排失败", e);
            return error("修改值班安排失败：" + e.getMessage());
        }
    }
    
    /**
     * 删除值班安排
     */
    @ApiOperation("删除值班安排")
    @PreAuthorize("@ss.hasPermi('duty:schedule:remove')")
    @Log(title = "值班安排", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(
            @ApiParam(value = "值班安排ID数组", required = true)
            @PathVariable String[] ids) {
        try {
            int result = dutyScheduleService.deleteDutyScheduleByIds(ids);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("删除值班安排失败", e);
            return error("删除值班安排失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询指定日期的值班安排
     */
    @ApiOperation("查询指定日期的值班安排")
    @PreAuthorize("@ss.hasPermi('duty:schedule:query')")
    @GetMapping("/date/{date}")
    public AjaxResult getByDate(
            @ApiParam(value = "值班日期(yyyy-MM-dd)", required = true)
            @PathVariable String date) {
        if (StringUtils.isEmpty(date)) {
            return error("值班日期不能为空");
        }
        try {
            List<EmDutyScheduleDetailVO> list = dutyScheduleService.selectDutyScheduleByDate(date);
            return success(list);
        } catch (Exception e) {
            logger.error("查询指定日期值班安排失败", e);
            return error("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询当前生效的值班安排中的所有值班人员
     */
    @ApiOperation("查询当前生效的值班安排中的所有值班人员")
    @PreAuthorize("@ss.hasPermi('duty:schedule:query')")
    @GetMapping("/current")
    public AjaxResult getCurrentDutyPersons(EmDutyScheduleDTO dutySchedule) {
        try {
            List<EmDutyPersonVO> list = dutyScheduleService.selectCurrentDutyPersons(dutySchedule);
            return success(list);
        } catch (Exception e) {
            logger.error("查询当前值班人员失败", e);
            return error("查询失败：" + e.getMessage());
        }
    }
    
    // ========== 值班人员管理接口 ==========
    
    /**
     * 添加值班人员
     */
    @ApiOperation("添加值班人员")
    @PreAuthorize("@ss.hasPermi('duty:person:add')")
    @Log(title = "值班人员", businessType = BusinessType.INSERT)
    @PostMapping("/person/add")
    public AjaxResult addPerson(@Valid @RequestBody EmDutyPersonDTO dutyPerson) {
        try {
            int result = dutyScheduleService.addDutyPerson(dutyPerson);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("添加值班人员失败", e);
            return error("添加值班人员失败：" + e.getMessage());
        }
    }
    
    /**
     * 批量添加值班人员
     */
    @ApiOperation("批量添加值班人员")
    @PreAuthorize("@ss.hasPermi('duty:person:add')")
    @Log(title = "值班人员", businessType = BusinessType.INSERT)
    @PostMapping("/person/addBatch")
    public AjaxResult addPersonBatch(@Valid @RequestBody List<EmDutyPersonDTO> dutyPersons) {
        try {
            int result = dutyScheduleService.addDutyPersonBatch(dutyPersons);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("批量添加值班人员失败", e);
            return error("批量添加值班人员失败：" + e.getMessage());
        }
    }
    
    /**
     * 修改值班人员
     */
    @ApiOperation("修改值班人员")
    @PreAuthorize("@ss.hasPermi('duty:person:edit')")
    @Log(title = "值班人员", businessType = BusinessType.UPDATE)
    @PutMapping("/person/update")
    public AjaxResult updatePerson(@Valid @RequestBody EmDutyPersonDTO dutyPerson) {
        try {
            int result = dutyScheduleService.updateDutyPerson(dutyPerson);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("修改值班人员失败", e);
            return error("修改值班人员失败：" + e.getMessage());
        }
    }
    
    /**
     * 删除值班人员
     */
    @ApiOperation("删除值班人员")
    @PreAuthorize("@ss.hasPermi('duty:person:remove')")
    @Log(title = "值班人员", businessType = BusinessType.DELETE)
    @DeleteMapping("/person/{ids}")
    public AjaxResult removePerson(
            @ApiParam(value = "值班人员ID数组", required = true)
            @PathVariable String[] ids) {
        try {
            int result = dutyScheduleService.deleteDutyPersonByIds(ids);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("删除值班人员失败", e);
            return error("删除值班人员失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询个人值班安排
     */
    @ApiOperation("查询个人值班安排")
    @PreAuthorize("@ss.hasPermi('duty:person:query')")
    @GetMapping("/person/my")
    public AjaxResult getMyDutySchedule(EmDutyPersonDTO dutyPerson) {
        try {
            List<EmDutyPersonVO> list = dutyScheduleService.selectMyDutySchedule(dutyPerson);
            return success(list);
        } catch (Exception e) {
            logger.error("查询个人值班安排失败", e);
            return error("查询失败：" + e.getMessage());
        }
    }
}
