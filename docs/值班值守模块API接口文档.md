# 值班值守模块API接口文档

## 1. 概述

值班值守模块提供完整的值班安排管理功能，包括值班安排的创建、查询、修改、删除，以及值班人员的管理。

**基础路径：** `/duty/schedule`

**权限前缀：** `duty:schedule` 和 `duty:person`

## 2. 值班安排管理接口

### 2.1 查询值班安排列表

**接口地址：** `GET /duty/schedule/list`

**权限要求：** `duty:schedule:list`

**请求参数：**
```javascript
{
  "scheduleName": "string",      // 值班安排名称（模糊查询）
  "startDate": "2024-06-01",     // 开始日期（yyyy-MM-dd）
  "endDate": "2024-06-30",       // 结束日期（yyyy-MM-dd）
  "status": "0",                 // 状态（0-正常，1-取消）
  "creatorName": "string",       // 创建人姓名（模糊查询）
  "creatorDeptName": "string",   // 创建人单位名称（模糊查询）
  "creatorDeptId": 103,          // 创建人单位ID（精确查询）
  "pageNum": 1,                  // 页码
  "pageSize": 10                 // 每页数量
}
```

**响应示例：**
```javascript
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "id": "uuid-001",
      "scheduleName": "2024年6月值班安排",
      "startDate": "2024-06-01",
      "endDate": "2024-06-30",
      "creatorName": "张三",
      "creatorDeptName": "应急管理局",
      "dayShiftTime": "白班:08:00:00-18:00:00 夜班:18:00:00-08:00:00",
      "contactPhone": "12345678901",
      "status": "0",
      "statusName": "正常",
      "totalDays": 30,
      "personCount": 15,
      "createTime": "2024-06-01 10:00:00",
      "remark": "备注信息"
    }
  ],
  "total": 1,
  "pageNum": 1,
  "pageSize": 10
}
```

### 2.2 获取值班安排详情

**接口地址：** `GET /duty/schedule/{id}`

**权限要求：** `duty:schedule:query`

**路径参数：**
- `id`: 值班安排ID

**响应示例：**
```javascript
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": "uuid-001",
    "scheduleName": "2024年6月值班安排",
    "startDate": "2024-06-01",
    "endDate": "2024-06-30",
    "creatorName": "张三",
    "creatorDeptName": "应急管理局",
    "dayShiftStartTime": "08:00:00",
    "dayShiftEndTime": "18:00:00",
    "nightShiftStartTime": "18:00:00",
    "nightShiftEndTime": "08:00:00",
    "fullShiftStartTime": "00:00:00",
    "fullShiftEndTime": "23:59:59",
    "contactPhone": "12345678901",
    "dutyRequirements": "值班要求",
    "dutyResponsibilities": "值班职责",
    "status": "0",
    "statusName": "正常",
    "totalDays": 30,
    "createTime": "2024-06-01 10:00:00",
    "remark": "备注信息",
    "dutyPersons": [
      {
        "id": "person-001",
        "dutyDate": "2024-06-01",
        "shiftType": "1",
        "shiftTypeName": "白班",
        "startTime": "08:00:00-18:00:00",
        "personName": "李四",
        "personPosition": "值班员",
        "personUnit": "应急管理局",
        "personType": "2",
        "personTypeName": "值班员",
        "contactPhone": "13800138000"
      }
    ]
  }
}
```

### 2.3 创建值班安排

**接口地址：** `POST /duty/schedule/create`

**权限要求：** `duty:schedule:add`

**请求体：**
```javascript
{
  "scheduleName": "2024年7月值班安排",
  "startDate": "2024-07-01",
  "endDate": "2024-07-31",
  "dayShiftStartTime": "08:00:00",
  "dayShiftEndTime": "18:00:00",
  "nightShiftStartTime": "18:00:00",
  "nightShiftEndTime": "08:00:00",
  "fullShiftStartTime": "00:00:00",
  "fullShiftEndTime": "23:59:59",
  "contactPhone": "12345678901",
  "dutyRequirements": "值班要求",
  "dutyResponsibilities": "值班职责",
  "remark": "备注信息",
  "dutyPersons": [
    {
      "dutyDate": "2024-07-01",
      "shiftType": "1",
      "personName": "张三",
      "personPosition": "值班领导",
      "personUnit": "应急管理局",
      "personType": "1",
      "contactPhone": "13800138001",
      "remark": "值班领导"
    },
    {
      "dutyDate": "2024-07-01",
      "shiftType": "1",
      "personName": "李四",
      "personPosition": "值班员",
      "personUnit": "应急管理局",
      "personType": "2",
      "contactPhone": "13800138002",
      "remark": "值班员"
    }
  ]
}
```

**响应示例：**
```javascript
{
  "code": 200,
  "msg": "操作成功"
}
```

### 2.4 修改值班安排

**接口地址：** `PUT /duty/schedule/update`

**权限要求：** `duty:schedule:edit`

**说明：** 支持同时修改值班安排基本信息和值班人员信息。如果请求体中包含`dutyPersons`字段，会同时更新人员信息。

**请求体（仅修改基本信息）：**
```javascript
{
  "id": "uuid-001",
  "scheduleName": "2024年6月值班安排（修改）",
  "startDate": "2024-06-01",
  "endDate": "2024-06-30",
  "dayShiftStartTime": "08:30:00",
  "dayShiftEndTime": "17:30:00",
  "contactPhone": "12345678902",
  "dutyRequirements": "新的值班要求",
  "dutyResponsibilities": "新的值班职责",
  "remark": "修改后的备注"
}
```

**请求体（同时修改基本信息和人员信息）：**
```javascript
{
  "id": "uuid-001",
  "scheduleName": "2024年6月值班安排（修改）",
  "startDate": "2024-06-01",
  "endDate": "2024-06-30",
  "dayShiftStartTime": "08:30:00",
  "dayShiftEndTime": "17:30:00",
  "contactPhone": "12345678902",
  "dutyRequirements": "新的值班要求",
  "dutyResponsibilities": "新的值班职责",
  "remark": "修改后的备注",
  "dutyPersons": [
    {
      "id": "person-001",  // 有ID表示更新现有人员
      "dutyDate": "2024-06-01",
      "shiftType": "1",
      "personName": "张三（修改）",
      "personType": "1",
      "contactPhone": "13800138001"
    },
    {
      // 没有ID表示新增人员
      "dutyDate": "2024-06-02",
      "shiftType": "2",
      "personName": "李四（新增）",
      "personType": "2",
      "contactPhone": "13800138002"
    }
  ]
}
```

**响应示例：**
```javascript
{
  "code": 200,
  "msg": "操作成功"
}
```

### 2.5 删除值班安排

**接口地址：** `DELETE /duty/schedule/{ids}`

**权限要求：** `duty:schedule:remove`

**路径参数：**
- `ids`: 值班安排ID数组，多个ID用逗号分隔

**响应示例：**
```javascript
{
  "code": 200,
  "msg": "操作成功"
}
```

## 3. 查询功能接口

### 3.1 查询指定日期的值班安排

**接口地址：** `GET /duty/schedule/date/{date}`

**权限要求：** `duty:schedule:query`

**路径参数：**
- `date`: 值班日期（yyyy-MM-dd格式）

**响应示例：**
```javascript
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": "uuid-001",
      "scheduleName": "2024年6月值班安排",
      "startDate": "2024-06-01",
      "endDate": "2024-06-30",
      "creatorName": "张三",
      "creatorDeptName": "应急管理局",
      "dayShiftStartTime": "08:00:00",
      "dayShiftEndTime": "18:00:00",
      "contactPhone": "12345678901",
      "dutyPersons": [
        {
          "id": "person-001",
          "dutyDate": "2024-06-21",
          "shiftType": "1",
          "shiftTypeName": "白班",
          "personName": "李四",
          "personType": "2",
          "personTypeName": "值班员",
          "contactPhone": "13800138000"
        }
      ]
    }
  ]
}
```

### 3.2 查询当前值班人员

**接口地址：** `GET /duty/schedule/current`

**权限要求：** `duty:schedule:query`

**请求参数：**
```javascript
{
  "scheduleName": "string",      // 值班安排名称（模糊查询）
  "startDate": "2024-06-01",     // 开始日期（yyyy-MM-dd）
  "endDate": "2024-06-30",       // 结束日期（yyyy-MM-dd）
  "status": "0",                 // 状态（0-正常，1-取消）
  "creatorName": "string",       // 创建人姓名（模糊查询）
  "creatorDeptName": "string"    // 创建人单位名称（模糊查询）
}
```

**响应示例：**
```javascript
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": "person-001",
      "scheduleId": "uuid-001",
      "scheduleName": "2024年6月值班安排",
      "dutyDate": "2024-06-21",
      "shiftType": "1",
      "shiftTypeName": "白班",
      "startTime": "08:00:00-18:00:00",
      "personName": "李四",
      "personPosition": "值班员",
      "personUnit": "应急管理局",
      "personType": "2",
      "personTypeName": "值班员",
      "contactPhone": "13800138000"
    }
  ]
}
```

## 4. 值班人员管理接口

### 4.1 添加值班人员

**接口地址：** `POST /duty/schedule/person/add`

**权限要求：** `duty:person:add`

**请求体：**
```javascript
{
  "scheduleId": "uuid-001",
  "dutyDate": "2024-06-22",
  "shiftType": "2",
  "personName": "王五",
  "personPosition": "值班员",
  "personUnit": "应急管理局",
  "personType": "2",
  "contactPhone": "13800138003",
  "remark": "临时添加"
}
```

**响应示例：**
```javascript
{
  "code": 200,
  "msg": "操作成功"
}
```

### 4.2 批量添加值班人员

**接口地址：** `POST /duty/schedule/person/addBatch`

**权限要求：** `duty:person:add`

**请求体：**
```javascript
[
  {
    "scheduleId": "uuid-001",
    "dutyDate": "2024-06-23",
    "shiftType": "1",
    "personName": "赵六",
    "personPosition": "值班员",
    "personUnit": "应急管理局",
    "personType": "2",
    "contactPhone": "13800138004"
  },
  {
    "scheduleId": "uuid-001",
    "dutyDate": "2024-06-23",
    "shiftType": "2",
    "personName": "孙七",
    "personPosition": "值班员",
    "personUnit": "应急管理局",
    "personType": "2",
    "contactPhone": "13800138005"
  }
]
```

**响应示例：**
```javascript
{
  "code": 200,
  "msg": "操作成功"
}
```

### 4.3 修改值班人员

**接口地址：** `PUT /duty/schedule/person/update`

**权限要求：** `duty:person:edit`

**请求体：**
```javascript
{
  "id": "person-001",
  "scheduleId": "uuid-001",
  "dutyDate": "2024-06-21",
  "shiftType": "1",
  "personName": "李四（修改）",
  "personPosition": "高级值班员",
  "personUnit": "应急管理局",
  "personType": "2",
  "contactPhone": "13800138000",
  "remark": "修改后的备注"
}
```

**响应示例：**
```javascript
{
  "code": 200,
  "msg": "操作成功"
}
```

### 4.4 删除值班人员

**接口地址：** `DELETE /duty/schedule/person/{ids}`

**权限要求：** `duty:person:remove`

**路径参数：**
- `ids`: 值班人员ID数组，多个ID用逗号分隔

**响应示例：**
```javascript
{
  "code": 200,
  "msg": "操作成功"
}
```

### 4.5 查询个人值班安排

**接口地址：** `GET /duty/schedule/person/my`

**权限要求：** `duty:person:query`

**请求参数：**
```javascript
{
  "personName": "李四",        // 值班人员姓名
  "dutyDate": "2024-06-01",    // 值班日期（查询此日期之后的安排）
  "shiftType": "1",            // 班次类型
  "personType": "2"            // 人员类型
}
```

**响应示例：**
```javascript
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": "person-001",
      "scheduleId": "uuid-001",
      "scheduleName": "2024年6月值班安排",
      "dutyDate": "2024-06-21",
      "shiftType": "1",
      "shiftTypeName": "白班",
      "startTime": "08:00:00-18:00:00",
      "personName": "李四",
      "personPosition": "值班员",
      "personUnit": "应急管理局",
      "personType": "2",
      "personTypeName": "值班员",
      "contactPhone": "13800138000",
      "createTime": "2024-06-01 10:00:00"
    }
  ]
}
```

## 5. 数据字典

### 5.1 班次类型 (shift_type)
| 值 | 标签 | 说明 |
|---|------|------|
| 1 | 白班 | 日间值班 |
| 2 | 夜班 | 夜间值班 |
| 3 | 全天 | 24小时值班 |

### 5.2 人员类型 (duty_person_type)
| 值 | 标签 | 说明 |
|---|------|------|
| 1 | 值班领导 | 值班领导 |
| 2 | 值班员 | 值班员 |
| 3 | 备班人员 | 备班人员 |

### 5.3 值班安排状态 (duty_schedule_status)
| 值 | 标签 | 说明 |
|---|------|------|
| 0 | 正常 | 正常状态 |
| 1 | 取消 | 取消状态 |

## 6. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 500 | 系统内部错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 400 | 请求参数错误 |

## 7. 注意事项

1. **时间格式：** 所有日期使用 `yyyy-MM-dd` 格式，时间使用 `HH:mm:ss` 格式
2. **权限控制：** 所有接口都需要相应的权限，请确保用户具有对应权限
3. **数据权限：** 用户只能查看和管理本单位创建的值班安排
4. **事务处理：** 创建和删除操作会自动处理主从表的数据一致性
5. **当前值班查询：** 会根据当前时间和班次时间自动判断正在值班的人员
6. **级联删除：** 删除值班安排时会自动删除相关的值班人员记录

## 8. 使用示例

### 8.1 创建完整的值班安排流程

```javascript
// 1. 创建值班安排（包含初始人员）
const createSchedule = {
  scheduleName: "2024年7月值班安排",
  startDate: "2024-07-01",
  endDate: "2024-07-31",
  dayShiftStartTime: "08:00:00",
  dayShiftEndTime: "18:00:00",
  dutyPersons: [
    {
      dutyDate: "2024-07-01",
      shiftType: "1",
      personName: "张三",
      personType: "1",
      contactPhone: "13800138001"
    }
  ]
};

// 2. 后续添加更多人员
const addPersons = [
  {
    scheduleId: "返回的scheduleId",
    dutyDate: "2024-07-02",
    shiftType: "1",
    personName: "李四",
    personType: "2",
    contactPhone: "13800138002"
  }
];
```

### 8.2 查询今日值班人员

```javascript
// 获取今日日期
const today = new Date().toISOString().split('T')[0];

// 查询今日值班安排
fetch(`/duty/schedule/date/${today}`)
  .then(response => response.json())
  .then(data => {
    console.log('今日值班安排:', data.data);
  });

// 或者直接查询当前正在值班的人员
fetch('/duty/schedule/current')
  .then(response => response.json())
  .then(data => {
    console.log('当前值班人员:', data.data);
  });
```

### 8.3 分页查询值班安排

```javascript
// 查询值班安排列表（带分页和条件）
const params = new URLSearchParams({
  scheduleName: '6月',
  status: '0',
  pageNum: 1,
  pageSize: 10
});

fetch(`/duty/schedule/list?${params}`)
  .then(response => response.json())
  .then(data => {
    console.log('值班安排列表:', data.rows);
    console.log('总数:', data.total);
  });
```
```
