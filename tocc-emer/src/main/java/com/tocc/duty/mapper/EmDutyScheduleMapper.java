package com.tocc.duty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tocc.duty.domain.dto.EmDutyScheduleDTO;
import com.tocc.duty.domain.entity.EmDutySchedule;
import com.tocc.duty.domain.vo.EmDutyPersonVO;
import com.tocc.duty.domain.vo.EmDutyScheduleDetailVO;
import com.tocc.duty.domain.vo.EmDutyScheduleVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 值班安排Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface EmDutyScheduleMapper extends BaseMapper<EmDutySchedule> {
    
    /**
     * 查询值班安排列表
     * 
     * @param dutySchedule 值班安排查询条件
     * @return 值班安排列表
     */
    List<EmDutyScheduleVO> selectDutyScheduleList(EmDutyScheduleDTO dutySchedule);
    
    /**
     * 查询值班安排详细信息
     * 
     * @param id 值班安排ID
     * @return 值班安排详细信息
     */
    EmDutyScheduleDetailVO selectDutyScheduleDetailById(@Param("id") String id);
    
    /**
     * 查询指定日期的值班安排
     * 
     * @param dutyDate 值班日期
     * @return 值班安排列表
     */
    List<EmDutyScheduleDetailVO> selectDutyScheduleByDate(@Param("dutyDate") String dutyDate);
    
    /**
     * 查询当前值班人员
     *
     * @param dutySchedule 筛选条件
     * @return 当前值班人员列表
     */
    List<EmDutyPersonVO> selectCurrentDutyPersons(EmDutyScheduleDTO dutySchedule);

    /**
     * 查询值班安排导出数据（扁平化结构）
     *
     * @param dutySchedule 查询条件
     * @return 导出数据列表
     */
    List<EmDutyScheduleExportVO> selectDutyScheduleExportList(EmDutyScheduleDTO dutySchedule);
    
    /**
     * 根据单位查询值班安排
     * 
     * @param deptId 单位ID
     * @return 值班安排列表
     */
    List<EmDutyScheduleVO> selectDutyScheduleByDept(@Param("deptId") Long deptId);
}
