package com.tocc.duty.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tocc.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 值班人员实体类 em_duty_person
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("em_duty_person")
@ApiModel(value = "值班人员实体")
public class EmDutyPerson extends BaseEntity {
    
    /** 主键ID */
    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("主键ID")
    private String id;
    
    /** 值班安排ID */
    @ApiModelProperty("值班安排ID")
    private String scheduleId;
    
    /** 值班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("值班日期")
    private Date dutyDate;
    
    /** 班次类型（1-白班，2-夜班，3-全天） */
    @ApiModelProperty("班次类型")
    private String shiftType;
    
    /** 值班人员姓名 */
    @ApiModelProperty("值班人员姓名")
    private String personName;
    
    /** 职务 */
    @ApiModelProperty("职务")
    private String personPosition;
    
    /** 所属单位 */
    @ApiModelProperty("所属单位")
    private String personUnit;
    
    /** 人员类型（1-值班领导，2-值班员，3-备班人员） */
    @ApiModelProperty("人员类型")
    private String personType;
    
    /** 联系电话 */
    @ApiModelProperty("联系电话")
    private String contactPhone;
    
    /** 删除标志（0-正常，1-删除） */
    @TableLogic
    @ApiModelProperty("删除标志")
    private Integer delFlag;

    /**
     * 重写getParams方法，添加@TableField(exist = false)注解
     * 防止MyBatis-Plus尝试将params字段映射到数据库
     */
    @Override
    @TableField(exist = false)
    public java.util.Map<String, Object> getParams() {
        return super.getParams();
    }
}
