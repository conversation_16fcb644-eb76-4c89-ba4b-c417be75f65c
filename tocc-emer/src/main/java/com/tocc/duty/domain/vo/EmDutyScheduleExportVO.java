package com.tocc.duty.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tocc.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Date;

/**
 * 值班安排导出VO（扁平化结构，包含值班安排和人员信息）
 * 
 * <AUTHOR>
 */
@Data
@ApiModel("值班安排导出VO")
public class EmDutyScheduleExportVO {
    
    // ========== 值班安排信息 ==========
    
    @Excel(name = "值班安排名称", sort = 1)
    @ApiModelProperty("值班安排名称")
    private String scheduleName;
    
    @Excel(name = "开始日期", sort = 2, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("开始日期")
    private LocalDate startDate;
    
    @Excel(name = "结束日期", sort = 3, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("结束日期")
    private LocalDate endDate;
    
    @Excel(name = "白班开始时间", sort = 4)
    @ApiModelProperty("白班开始时间")
    private String dayShiftStartTime;
    
    @Excel(name = "白班结束时间", sort = 5)
    @ApiModelProperty("白班结束时间")
    private String dayShiftEndTime;
    
    @Excel(name = "夜班开始时间", sort = 6)
    @ApiModelProperty("夜班开始时间")
    private String nightShiftStartTime;
    
    @Excel(name = "夜班结束时间", sort = 7)
    @ApiModelProperty("夜班结束时间")
    private String nightShiftEndTime;
    
    @Excel(name = "全天班开始时间", sort = 8)
    @ApiModelProperty("全天班开始时间")
    private String fullShiftStartTime;
    
    @Excel(name = "全天班结束时间", sort = 9)
    @ApiModelProperty("全天班结束时间")
    private String fullShiftEndTime;
    
    @Excel(name = "值班电话", sort = 10)
    @ApiModelProperty("值班电话")
    private String contactPhone;
    
    @Excel(name = "值班要求", sort = 11, width = 30)
    @ApiModelProperty("值班要求")
    private String dutyRequirements;
    
    @Excel(name = "值班职责", sort = 12, width = 30)
    @ApiModelProperty("值班职责")
    private String dutyResponsibilities;
    
    @Excel(name = "创建人", sort = 13)
    @ApiModelProperty("创建人")
    private String creatorName;
    
    @Excel(name = "创建单位", sort = 14, width = 25)
    @ApiModelProperty("创建单位")
    private String creatorDeptName;
    
    @Excel(name = "安排备注", sort = 15, width = 25)
    @ApiModelProperty("安排备注")
    private String scheduleRemark;
    
    // ========== 值班人员信息 ==========
    
    @Excel(name = "值班日期", sort = 16, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("值班日期")
    private LocalDate dutyDate;
    
    @Excel(name = "班次类型", sort = 17, dictType = "shift_type")
    @ApiModelProperty("班次类型")
    private String shiftType;
    
    @Excel(name = "班次名称", sort = 18)
    @ApiModelProperty("班次名称")
    private String shiftTypeName;
    
    @Excel(name = "班次时间", sort = 19)
    @ApiModelProperty("班次时间")
    private String shiftTime;
    
    @Excel(name = "人员姓名", sort = 20)
    @ApiModelProperty("人员姓名")
    private String personName;
    
    @Excel(name = "职务", sort = 21)
    @ApiModelProperty("职务")
    private String personPosition;
    
    @Excel(name = "所属单位", sort = 22, width = 25)
    @ApiModelProperty("所属单位")
    private String personUnit;
    
    @Excel(name = "人员类型", sort = 23, dictType = "duty_person_type")
    @ApiModelProperty("人员类型")
    private String personType;
    
    @Excel(name = "人员类型名称", sort = 24)
    @ApiModelProperty("人员类型名称")
    private String personTypeName;
    
    @Excel(name = "联系电话", sort = 25)
    @ApiModelProperty("联系电话")
    private String personContactPhone;
    
    @Excel(name = "人员备注", sort = 26, width = 25)
    @ApiModelProperty("人员备注")
    private String personRemark;
    
    @Excel(name = "创建时间", sort = 27, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
