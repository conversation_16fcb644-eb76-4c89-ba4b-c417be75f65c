package com.tocc.duty.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tocc.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Time;
import java.util.Date;

/**
 * 值班安排实体类 em_duty_schedule
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("em_duty_schedule")
@ApiModel(value = "值班安排实体")
public class EmDutySchedule extends BaseEntity {
    
    /** 主键ID */
    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("主键ID")
    private String id;
    
    /** 值班安排名称 */
    @ApiModelProperty("值班安排名称")
    private String scheduleName;
    
    /** 开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("开始日期")
    private Date startDate;
    
    /** 结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("结束日期")
    private Date endDate;
    
    /** 创建人用户ID */
    @ApiModelProperty("创建人用户ID")
    private Long creatorUserId;
    
    /** 创建人单位ID */
    @ApiModelProperty("创建人单位ID")
    private Long creatorDeptId;
    
    /** 白班开始时间 */
    @JsonFormat(pattern = "HH:mm:ss")
    @ApiModelProperty("白班开始时间")
    private Time dayShiftStartTime;
    
    /** 白班结束时间 */
    @JsonFormat(pattern = "HH:mm:ss")
    @ApiModelProperty("白班结束时间")
    private Time dayShiftEndTime;
    
    /** 夜班开始时间 */
    @JsonFormat(pattern = "HH:mm:ss")
    @ApiModelProperty("夜班开始时间")
    private Time nightShiftStartTime;
    
    /** 夜班结束时间 */
    @JsonFormat(pattern = "HH:mm:ss")
    @ApiModelProperty("夜班结束时间")
    private Time nightShiftEndTime;
    
    /** 全天班开始时间 */
    @JsonFormat(pattern = "HH:mm:ss")
    @ApiModelProperty("全天班开始时间")
    private Time fullShiftStartTime;
    
    /** 全天班结束时间 */
    @JsonFormat(pattern = "HH:mm:ss")
    @ApiModelProperty("全天班结束时间")
    private Time fullShiftEndTime;
    

    
    /** 值班电话 */
    @ApiModelProperty("值班电话")
    private String contactPhone;
    
    /** 值班要求 */
    @ApiModelProperty("值班要求")
    private String dutyRequirements;
    
    /** 值班职责 */
    @ApiModelProperty("值班职责")
    private String dutyResponsibilities;
    
    /** 状态（0-正常，1-取消） */
    @ApiModelProperty("状态")
    private String status;
    
    /** 删除标志（0-正常，1-删除） */
    @TableLogic
    @ApiModelProperty("删除标志")
    private Integer delFlag;
}
