package com.tocc.duty.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 值班安排VO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel("值班安排VO")
public class EmDutyScheduleVO {
    
    @ApiModelProperty("主键ID")
    private String id;
    
    @ApiModelProperty("值班安排名称")
    private String scheduleName;
    
    @ApiModelProperty("开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;
    
    @ApiModelProperty("结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;
    
    @ApiModelProperty("创建人姓名")
    private String creatorName;
    
    @ApiModelProperty("创建人单位名称")
    private String creatorDeptName;
    
    // 班次时间配置
    @ApiModelProperty("白班时间")
    private String dayShiftTime;
    
    @ApiModelProperty("夜班时间")
    private String nightShiftTime;
    
    @ApiModelProperty("全天班时间")
    private String fullShiftTime;
    

    
    @ApiModelProperty("值班电话")
    private String contactPhone;
    
    @ApiModelProperty("状态")
    private String status;
    
    @ApiModelProperty("状态名称")
    private String statusName;
    
    @ApiModelProperty("总天数")
    private Integer totalDays;
    
    @ApiModelProperty("已安排人员数量")
    private Integer personCount;
    
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    @ApiModelProperty("备注")
    private String remark;
}
