package com.tocc.duty.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.sql.Time;
import java.util.Date;

/**
 * 值班安排DTO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel("值班安排DTO")
public class EmDutyScheduleDTO {
    
    @ApiModelProperty("主键ID")
    private String id;
    
    @ApiModelProperty("值班安排名称")
    @NotBlank(message = "值班安排名称不能为空")
    @Size(max = 200, message = "值班安排名称长度不能超过200个字符")
    private String scheduleName;
    
    @ApiModelProperty("开始日期")
    @NotNull(message = "开始日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;
    
    @ApiModelProperty("结束日期")
    @NotNull(message = "结束日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;
    
    @ApiModelProperty("创建人用户ID")
    private Long creatorUserId;
    
    @ApiModelProperty("创建人单位ID")
    private Long creatorDeptId;
    
    // 班次时间配置
    @ApiModelProperty("白班开始时间")
    @JsonFormat(pattern = "HH:mm:ss")
    private Time dayShiftStartTime;
    
    @ApiModelProperty("白班结束时间")
    @JsonFormat(pattern = "HH:mm:ss")
    private Time dayShiftEndTime;
    
    @ApiModelProperty("夜班开始时间")
    @JsonFormat(pattern = "HH:mm:ss")
    private Time nightShiftStartTime;
    
    @ApiModelProperty("夜班结束时间")
    @JsonFormat(pattern = "HH:mm:ss")
    private Time nightShiftEndTime;
    
    @ApiModelProperty("全天班开始时间")
    @JsonFormat(pattern = "HH:mm:ss")
    private Time fullShiftStartTime;
    
    @ApiModelProperty("全天班结束时间")
    @JsonFormat(pattern = "HH:mm:ss")
    private Time fullShiftEndTime;
    

    
    @ApiModelProperty("值班电话")
    private String contactPhone;
    
    @ApiModelProperty("值班要求")
    private String dutyRequirements;
    
    @ApiModelProperty("值班职责")
    private String dutyResponsibilities;
    
    @ApiModelProperty("状态")
    private String status;
    
    @ApiModelProperty("备注")
    private String remark;
    
    // 查询条件
    @ApiModelProperty("创建人姓名（查询条件）")
    private String creatorName;

    @ApiModelProperty("创建人单位名称（查询条件）")
    private String creatorDeptName;
}
