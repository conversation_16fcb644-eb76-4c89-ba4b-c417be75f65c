package com.tocc.duty.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 值班人员DTO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel("值班人员DTO")
public class EmDutyPersonDTO {
    
    @ApiModelProperty("主键ID")
    private String id;
    
    @ApiModelProperty("值班安排ID")
    @NotBlank(message = "值班安排ID不能为空")
    private String scheduleId;
    
    @ApiModelProperty("值班日期")
    @NotNull(message = "值班日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dutyDate;
    
    @ApiModelProperty("班次类型（1-白班，2-夜班，3-全天）")
    @NotBlank(message = "班次类型不能为空")
    @Pattern(regexp = "^[123]$", message = "班次类型只能是1、2、3")
    private String shiftType;
    
    @ApiModelProperty("值班人员姓名")
    @NotBlank(message = "值班人员姓名不能为空")
    @Size(max = 50, message = "姓名长度不能超过50个字符")
    private String personName;
    
    @ApiModelProperty("职务")
    @Size(max = 100, message = "职务长度不能超过100个字符")
    private String personPosition;
    
    @ApiModelProperty("所属单位")
    @Size(max = 200, message = "所属单位长度不能超过200个字符")
    private String personUnit;
    
    @ApiModelProperty("人员类型（1-值班领导，2-值班员，3-备班人员）")
    @NotBlank(message = "人员类型不能为空")
    @Pattern(regexp = "^[123]$", message = "人员类型只能是1、2、3")
    private String personType;
    
    @ApiModelProperty("联系电话")
    @Pattern(regexp = "^[1-9]\\d{7,15}$", message = "联系电话格式不正确，请输入8-16位数字")
    private String contactPhone;
    
    @ApiModelProperty("备注")
    private String remark;
    
    // 查询条件
    @ApiModelProperty("值班安排名称（查询条件）")
    private String scheduleName;
}
