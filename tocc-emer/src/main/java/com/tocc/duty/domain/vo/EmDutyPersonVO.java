package com.tocc.duty.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 值班人员VO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel("值班人员VO")
public class EmDutyPersonVO {
    
    @ApiModelProperty("主键ID")
    private String id;
    
    @ApiModelProperty("值班安排ID")
    private String scheduleId;
    
    @ApiModelProperty("值班安排名称")
    private String scheduleName;
    
    @ApiModelProperty("值班日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dutyDate;
    
    @ApiModelProperty("班次类型")
    private String shiftType;
    
    @ApiModelProperty("班次类型名称")
    private String shiftTypeName;
    
    @ApiModelProperty("开始时间")
    private String startTime;
    
    @ApiModelProperty("结束时间")
    private String endTime;
    
    @ApiModelProperty("值班人员姓名")
    private String personName;
    
    @ApiModelProperty("职务")
    private String personPosition;
    
    @ApiModelProperty("所属单位")
    private String personUnit;
    
    @ApiModelProperty("人员类型")
    private String personType;
    
    @ApiModelProperty("人员类型名称")
    private String personTypeName;
    
    @ApiModelProperty("联系电话")
    private String contactPhone;
    

    
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    @ApiModelProperty("备注")
    private String remark;
}
