package com.tocc.duty.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Time;
import java.util.Date;
import java.util.List;

/**
 * 值班安排详情VO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel("值班安排详情VO")
public class EmDutyScheduleDetailVO {
    
    @ApiModelProperty("主键ID")
    private String id;
    
    @ApiModelProperty("值班安排名称")
    private String scheduleName;
    
    @ApiModelProperty("开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;
    
    @ApiModelProperty("结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;
    
    @ApiModelProperty("创建人用户ID")
    private Long creatorUserId;
    
    @ApiModelProperty("创建人单位ID")
    private Long creatorDeptId;
    
    @ApiModelProperty("创建人姓名")
    private String creatorName;
    
    @ApiModelProperty("创建人单位名称")
    private String creatorDeptName;
    
    // 班次时间配置
    @ApiModelProperty("白班开始时间")
    @JsonFormat(pattern = "HH:mm:ss")
    private Time dayShiftStartTime;
    
    @ApiModelProperty("白班结束时间")
    @JsonFormat(pattern = "HH:mm:ss")
    private Time dayShiftEndTime;
    
    @ApiModelProperty("夜班开始时间")
    @JsonFormat(pattern = "HH:mm:ss")
    private Time nightShiftStartTime;
    
    @ApiModelProperty("夜班结束时间")
    @JsonFormat(pattern = "HH:mm:ss")
    private Time nightShiftEndTime;
    
    @ApiModelProperty("全天班开始时间")
    @JsonFormat(pattern = "HH:mm:ss")
    private Time fullShiftStartTime;
    
    @ApiModelProperty("全天班结束时间")
    @JsonFormat(pattern = "HH:mm:ss")
    private Time fullShiftEndTime;
    

    
    @ApiModelProperty("值班电话")
    private String contactPhone;
    
    @ApiModelProperty("值班要求")
    private String dutyRequirements;
    
    @ApiModelProperty("值班职责")
    private String dutyResponsibilities;
    
    @ApiModelProperty("状态")
    private String status;
    
    @ApiModelProperty("状态名称")
    private String statusName;
    
    @ApiModelProperty("总天数")
    private Integer totalDays;
    
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    @ApiModelProperty("备注")
    private String remark;
    
    @ApiModelProperty("值班人员列表")
    private List<EmDutyPersonVO> dutyPersons;
}
