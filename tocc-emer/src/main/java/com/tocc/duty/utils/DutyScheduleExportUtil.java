package com.tocc.duty.utils;

import com.tocc.common.utils.StringUtils;
import com.tocc.duty.domain.vo.EmDutyPersonVO;
import com.tocc.duty.domain.vo.EmDutyScheduleDetailVO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 值班安排导出工具类
 * 
 * <AUTHOR>
 */
@Component
public class DutyScheduleExportUtil {
    
    /**
     * 导出值班安排及人员信息
     * 
     * @param scheduleList 值班安排列表（包含人员信息）
     * @param response HTTP响应
     */
    public void exportDutyScheduleWithPersons(List<EmDutyScheduleDetailVO> scheduleList, HttpServletResponse response) {
        try {
            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("值班安排及人员信息");
            
            // 创建样式
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);
            
            // 创建表头
            createHeaders(sheet, headerStyle);
            
            // 填充数据
            fillData(sheet, scheduleList, dataStyle);
            
            // 自动调整列宽
            autoSizeColumns(sheet);
            
            // 设置响应头
            setResponseHeaders(response, "值班安排及人员信息");
            
            // 输出文件
            workbook.write(response.getOutputStream());
            workbook.close();
            
        } catch (Exception e) {
            throw new RuntimeException("导出Excel文件失败", e);
        }
    }
    
    /**
     * 创建表头样式
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        
        // 设置背景色
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        
        // 设置字体
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 12);
        style.setFont(font);
        
        // 设置对齐方式
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        return style;
    }
    
    /**
     * 创建数据样式
     */
    private CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        
        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        
        // 设置对齐方式
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        // 设置自动换行
        style.setWrapText(true);
        
        return style;
    }
    
    /**
     * 创建表头
     */
    private void createHeaders(Sheet sheet, CellStyle headerStyle) {
        Row headerRow = sheet.createRow(0);
        
        String[] headers = {
            "值班安排名称", "开始日期", "结束日期", 
            "白班开始时间", "白班结束时间", "夜班开始时间", "夜班结束时间",
            "全天班开始时间", "全天班结束时间", "值班电话", 
            "值班要求", "值班职责", "安排备注",
            "值班日期", "班次类型", "班次名称",
            "人员姓名", "职务", "所属单位", 
            "人员类型", "人员类型名称", "联系电话", "人员备注"
        };
        
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
    }
    
    /**
     * 填充数据
     */
    private void fillData(Sheet sheet, List<EmDutyScheduleDetailVO> scheduleList, CellStyle dataStyle) {
        int rowIndex = 1;
        
        for (EmDutyScheduleDetailVO schedule : scheduleList) {
            List<EmDutyPersonVO> dutyPersons = schedule.getDutyPersons();
            
            if (dutyPersons != null && !dutyPersons.isEmpty()) {
                // 有人员信息，每个人员一行
                for (EmDutyPersonVO person : dutyPersons) {
                    Row row = sheet.createRow(rowIndex++);
                    fillRowData(row, schedule, person, dataStyle);
                }
            } else {
                // 没有人员信息，只显示值班安排信息
                Row row = sheet.createRow(rowIndex++);
                fillRowData(row, schedule, null, dataStyle);
            }
        }
    }
    
    /**
     * 填充行数据
     */
    private void fillRowData(Row row, EmDutyScheduleDetailVO schedule, EmDutyPersonVO person, CellStyle dataStyle) {
        int cellIndex = 0;
        
        // 值班安排信息
        createCell(row, cellIndex++, schedule.getScheduleName(), dataStyle);
        createCell(row, cellIndex++, schedule.getStartDate() != null ? schedule.getStartDate().toString() : "", dataStyle);
        createCell(row, cellIndex++, schedule.getEndDate() != null ? schedule.getEndDate().toString() : "", dataStyle);
        createCell(row, cellIndex++, schedule.getDayShiftStartTime() != null ? schedule.getDayShiftStartTime().toString() : "", dataStyle);
        createCell(row, cellIndex++, schedule.getDayShiftEndTime() != null ? schedule.getDayShiftEndTime().toString() : "", dataStyle);
        createCell(row, cellIndex++, schedule.getNightShiftStartTime() != null ? schedule.getNightShiftStartTime().toString() : "", dataStyle);
        createCell(row, cellIndex++, schedule.getNightShiftEndTime() != null ? schedule.getNightShiftEndTime().toString() : "", dataStyle);
        createCell(row, cellIndex++, schedule.getFullShiftStartTime() != null ? schedule.getFullShiftStartTime().toString() : "", dataStyle);
        createCell(row, cellIndex++, schedule.getFullShiftEndTime() != null ? schedule.getFullShiftEndTime().toString() : "", dataStyle);
        createCell(row, cellIndex++, StringUtils.isNotEmpty(schedule.getContactPhone()) ? schedule.getContactPhone() : "", dataStyle);
        createCell(row, cellIndex++, StringUtils.isNotEmpty(schedule.getDutyRequirements()) ? schedule.getDutyRequirements() : "", dataStyle);
        createCell(row, cellIndex++, StringUtils.isNotEmpty(schedule.getDutyResponsibilities()) ? schedule.getDutyResponsibilities() : "", dataStyle);
        createCell(row, cellIndex++, StringUtils.isNotEmpty(schedule.getRemark()) ? schedule.getRemark() : "", dataStyle);
        
        // 人员信息
        if (person != null) {
            createCell(row, cellIndex++, person.getDutyDate() != null ? person.getDutyDate().toString() : "", dataStyle);
            createCell(row, cellIndex++, StringUtils.isNotEmpty(person.getShiftType()) ? person.getShiftType() : "", dataStyle);
            createCell(row, cellIndex++, StringUtils.isNotEmpty(person.getShiftTypeName()) ? person.getShiftTypeName() : "", dataStyle);
            createCell(row, cellIndex++, StringUtils.isNotEmpty(person.getPersonName()) ? person.getPersonName() : "", dataStyle);
            createCell(row, cellIndex++, StringUtils.isNotEmpty(person.getPersonPosition()) ? person.getPersonPosition() : "", dataStyle);
            createCell(row, cellIndex++, StringUtils.isNotEmpty(person.getPersonUnit()) ? person.getPersonUnit() : "", dataStyle);
            createCell(row, cellIndex++, StringUtils.isNotEmpty(person.getPersonType()) ? person.getPersonType() : "", dataStyle);
            createCell(row, cellIndex++, StringUtils.isNotEmpty(person.getPersonTypeName()) ? person.getPersonTypeName() : "", dataStyle);
            createCell(row, cellIndex++, StringUtils.isNotEmpty(person.getContactPhone()) ? person.getContactPhone() : "", dataStyle);
            createCell(row, cellIndex++, StringUtils.isNotEmpty(person.getRemark()) ? person.getRemark() : "", dataStyle);
        } else {
            // 填充空的人员信息列
            for (int i = 0; i < 10; i++) {
                createCell(row, cellIndex++, "", dataStyle);
            }
        }
    }
    
    /**
     * 创建单元格
     */
    private void createCell(Row row, int cellIndex, String value, CellStyle style) {
        Cell cell = row.createCell(cellIndex);
        cell.setCellValue(value != null ? value : "");
        cell.setCellStyle(style);
    }
    
    /**
     * 自动调整列宽
     */
    private void autoSizeColumns(Sheet sheet) {
        for (int i = 0; i < 23; i++) {
            sheet.autoSizeColumn(i);
            // 设置最大列宽，避免过宽
            if (sheet.getColumnWidth(i) > 15000) {
                sheet.setColumnWidth(i, 15000);
            }
        }
    }
    
    /**
     * 设置响应头
     */
    private void setResponseHeaders(HttpServletResponse response, String fileName) throws IOException {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String fullFileName = fileName + "_" + timestamp + ".xlsx";
        
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Disposition", 
            "attachment; filename=" + URLEncoder.encode(fullFileName, "UTF-8"));
    }
}
