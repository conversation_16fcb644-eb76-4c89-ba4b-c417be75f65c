package com.tocc.duty.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tocc.common.core.domain.model.LoginUser;
import com.tocc.common.exception.ServiceException;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.utils.StringUtils;
import com.tocc.common.utils.bean.BeanUtils;
import com.tocc.common.utils.uuid.IdUtils;
import com.tocc.duty.domain.dto.EmDutyPersonCreateDTO;
import com.tocc.duty.domain.dto.EmDutyPersonDTO;
import com.tocc.duty.domain.dto.EmDutyScheduleCreateDTO;
import com.tocc.duty.domain.dto.EmDutyScheduleDTO;
import com.tocc.duty.domain.entity.EmDutyPerson;
import com.tocc.duty.domain.entity.EmDutySchedule;
import com.tocc.duty.domain.vo.EmDutyPersonVO;
import com.tocc.duty.domain.vo.EmDutyScheduleDetailVO;
import com.tocc.duty.domain.vo.EmDutyScheduleVO;
import com.tocc.duty.mapper.EmDutyPersonMapper;
import com.tocc.duty.mapper.EmDutyScheduleMapper;
import com.tocc.duty.service.IEmDutyScheduleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 值班安排Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class EmDutyScheduleServiceImpl implements IEmDutyScheduleService {
    
    @Autowired
    private EmDutyScheduleMapper dutyScheduleMapper;
    
    @Autowired
    private EmDutyPersonMapper dutyPersonMapper;
    
    /**
     * 创建值班安排（包含人员）
     * 
     * @param dutyScheduleCreate 创建值班安排DTO
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int createDutySchedule(EmDutyScheduleCreateDTO dutyScheduleCreate) {
        // 获取当前登录用户信息
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException("用户未登录");
        }
        
        // 创建值班安排主记录
        EmDutySchedule dutySchedule = new EmDutySchedule();
        BeanUtils.copyProperties(dutyScheduleCreate, dutySchedule);
        
        // 设置主键ID
        String scheduleId = IdUtils.fastSimpleUUID();
        dutySchedule.setId(scheduleId);
        
        // 设置创建人信息
        dutySchedule.setCreatorUserId(loginUser.getUserId());
        dutySchedule.setCreatorDeptId(loginUser.getUser().getDeptId());
        dutySchedule.setCreateBy(loginUser.getUsername());
        dutySchedule.setStatus("0"); // 默认状态为正常
        
        // 插入值班安排主记录
        int result = dutyScheduleMapper.insert(dutySchedule);
        if (result <= 0) {
            throw new ServiceException("创建值班安排失败");
        }
        
        // 批量插入值班人员
        List<EmDutyPersonCreateDTO> dutyPersons = dutyScheduleCreate.getDutyPersons();
        if (dutyPersons != null && !dutyPersons.isEmpty()) {
            List<EmDutyPerson> personList = new ArrayList<>();
            for (EmDutyPersonCreateDTO personCreateDTO : dutyPersons) {
                EmDutyPerson dutyPerson = new EmDutyPerson();
                BeanUtils.copyProperties(personCreateDTO, dutyPerson);
                
                dutyPerson.setId(IdUtils.fastSimpleUUID());
                dutyPerson.setScheduleId(scheduleId);
                dutyPerson.setCreateBy(loginUser.getUsername());
                
                personList.add(dutyPerson);
            }
            
            // 批量插入值班人员
            int personResult = dutyPersonMapper.insertBatch(personList);
            if (personResult <= 0) {
                throw new ServiceException("创建值班人员失败");
            }
        }
        
        return result;
    }
    
    /**
     * 查询值班安排列表
     *
     * @param dutySchedule 值班安排查询条件
     * @return 值班安排列表
     */
    @Override
    public List<EmDutyScheduleVO> selectDutyScheduleList(EmDutyScheduleDTO dutySchedule) {
        return dutyScheduleMapper.selectDutyScheduleList(dutySchedule);
    }
    
    /**
     * 查询值班安排详细信息
     * 
     * @param id 值班安排ID
     * @return 值班安排详细信息
     */
    @Override
    public EmDutyScheduleDetailVO selectDutyScheduleDetailById(String id) {
        if (StringUtils.isEmpty(id)) {
            throw new ServiceException("值班安排ID不能为空");
        }
        return dutyScheduleMapper.selectDutyScheduleDetailById(id);
    }
    
    /**
     * 修改值班安排基本信息
     * 
     * @param dutySchedule 值班安排
     * @return 结果
     */
    @Override
    public int updateDutySchedule(EmDutyScheduleDTO dutySchedule) {
        if (StringUtils.isEmpty(dutySchedule.getId())) {
            throw new ServiceException("值班安排ID不能为空");
        }
        
        // 检查值班安排是否存在
        EmDutySchedule existSchedule = dutyScheduleMapper.selectById(dutySchedule.getId());
        if (existSchedule == null) {
            throw new ServiceException("值班安排不存在");
        }
        
        // 转换DTO到实体
        EmDutySchedule updateSchedule = new EmDutySchedule();
        BeanUtils.copyProperties(dutySchedule, updateSchedule);
        updateSchedule.setUpdateBy(SecurityUtils.getUsername());
        
        return dutyScheduleMapper.updateById(updateSchedule);
    }
    
    /**
     * 批量删除值班安排
     * 
     * @param ids 需要删除的值班安排主键集合
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteDutyScheduleByIds(String[] ids) {
        if (ids == null || ids.length == 0) {
            throw new ServiceException("删除的值班安排ID不能为空");
        }
        
        // 软删除值班安排（MyBatis-Plus会自动处理逻辑删除）
        int result = dutyScheduleMapper.deleteBatchIds(Arrays.asList(ids));
        
        // 同时软删除相关的值班人员
        for (String id : ids) {
            LambdaQueryWrapper<EmDutyPerson> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(EmDutyPerson::getScheduleId, id);
            List<EmDutyPerson> personList = dutyPersonMapper.selectList(wrapper);
            if (!personList.isEmpty()) {
                List<String> personIds = new ArrayList<>();
                for (EmDutyPerson person : personList) {
                    personIds.add(person.getId());
                }
                dutyPersonMapper.deleteBatchIds(personIds);
            }
        }
        
        return result;
    }

    /**
     * 查询指定日期的值班安排
     *
     * @param dutyDate 值班日期
     * @return 值班安排列表
     */
    @Override
    public List<EmDutyScheduleDetailVO> selectDutyScheduleByDate(String dutyDate) {
        if (StringUtils.isEmpty(dutyDate)) {
            throw new ServiceException("值班日期不能为空");
        }
        return dutyScheduleMapper.selectDutyScheduleByDate(dutyDate);
    }

    /**
     * 查询当前值班人员
     *
     * @return 当前值班人员列表
     */
    @Override
    public List<EmDutyPersonVO> selectCurrentDutyPersons() {
        return dutyScheduleMapper.selectCurrentDutyPersons();
    }

    // ========== 值班人员管理（用于后续调整） ==========

    /**
     * 添加值班人员
     *
     * @param dutyPerson 值班人员
     * @return 结果
     */
    @Override
    public int addDutyPerson(EmDutyPersonDTO dutyPerson) {
        if (StringUtils.isEmpty(dutyPerson.getScheduleId())) {
            throw new ServiceException("值班安排ID不能为空");
        }

        // 检查值班安排是否存在
        EmDutySchedule schedule = dutyScheduleMapper.selectById(dutyPerson.getScheduleId());
        if (schedule == null) {
            throw new ServiceException("值班安排不存在");
        }

        // 转换DTO到实体
        EmDutyPerson person = new EmDutyPerson();
        BeanUtils.copyProperties(dutyPerson, person);
        person.setId(IdUtils.fastSimpleUUID());
        person.setCreateBy(SecurityUtils.getUsername());

        return dutyPersonMapper.insert(person);
    }

    /**
     * 批量添加值班人员
     *
     * @param dutyPersons 值班人员列表
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addDutyPersonBatch(List<EmDutyPersonDTO> dutyPersons) {
        if (dutyPersons == null || dutyPersons.isEmpty()) {
            throw new ServiceException("值班人员列表不能为空");
        }

        List<EmDutyPerson> personList = new ArrayList<>();
        String username = SecurityUtils.getUsername();

        for (EmDutyPersonDTO dutyPersonDTO : dutyPersons) {
            if (StringUtils.isEmpty(dutyPersonDTO.getScheduleId())) {
                throw new ServiceException("值班安排ID不能为空");
            }

            EmDutyPerson person = new EmDutyPerson();
            BeanUtils.copyProperties(dutyPersonDTO, person);
            person.setId(IdUtils.fastSimpleUUID());
            person.setCreateBy(username);

            personList.add(person);
        }

        return dutyPersonMapper.insertBatch(personList);
    }

    /**
     * 修改值班人员
     *
     * @param dutyPerson 值班人员
     * @return 结果
     */
    @Override
    public int updateDutyPerson(EmDutyPersonDTO dutyPerson) {
        if (StringUtils.isEmpty(dutyPerson.getId())) {
            throw new ServiceException("值班人员ID不能为空");
        }

        // 检查值班人员是否存在
        EmDutyPerson existPerson = dutyPersonMapper.selectById(dutyPerson.getId());
        if (existPerson == null) {
            throw new ServiceException("值班人员不存在");
        }

        // 转换DTO到实体
        EmDutyPerson updatePerson = new EmDutyPerson();
        BeanUtils.copyProperties(dutyPerson, updatePerson);
        updatePerson.setUpdateBy(SecurityUtils.getUsername());

        return dutyPersonMapper.updateById(updatePerson);
    }

    /**
     * 删除值班人员
     *
     * @param ids 需要删除的值班人员主键集合
     * @return 结果
     */
    @Override
    public int deleteDutyPersonByIds(String[] ids) {
        if (ids == null || ids.length == 0) {
            throw new ServiceException("删除的值班人员ID不能为空");
        }

        // 软删除值班人员（MyBatis-Plus会自动处理逻辑删除）
        return dutyPersonMapper.deleteBatchIds(Arrays.asList(ids));
    }

    /**
     * 查询个人值班安排
     *
     * @param dutyPerson 查询条件
     * @return 个人值班安排列表
     */
    @Override
    public List<EmDutyPersonVO> selectMyDutySchedule(EmDutyPersonDTO dutyPerson) {
        return dutyPersonMapper.selectMyDutySchedule(dutyPerson);
    }
}
