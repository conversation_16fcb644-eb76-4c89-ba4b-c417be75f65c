package com.tocc.duty.service;

import com.tocc.duty.domain.dto.EmDutyPersonDTO;
import com.tocc.duty.domain.dto.EmDutyScheduleCreateDTO;
import com.tocc.duty.domain.dto.EmDutyScheduleDTO;
import com.tocc.duty.domain.vo.EmDutyPersonVO;
import com.tocc.duty.domain.vo.EmDutyScheduleDetailVO;
import com.tocc.duty.domain.vo.EmDutyScheduleVO;

import java.util.List;

/**
 * 值班安排Service接口
 * 
 * <AUTHOR>
 */
public interface IEmDutyScheduleService {
    
    /**
     * 创建值班安排（包含人员）
     * 
     * @param dutyScheduleCreate 创建值班安排DTO
     * @return 结果
     */
    int createDutySchedule(EmDutyScheduleCreateDTO dutyScheduleCreate);
    
    /**
     * 查询值班安排列表
     * 
     * @param dutySchedule 值班安排查询条件
     * @return 值班安排列表
     */
    List<EmDutyScheduleVO> selectDutyScheduleList(EmDutyScheduleDTO dutySchedule);
    
    /**
     * 查询值班安排详细信息
     * 
     * @param id 值班安排ID
     * @return 值班安排详细信息
     */
    EmDutyScheduleDetailVO selectDutyScheduleDetailById(String id);
    
    /**
     * 修改值班安排基本信息
     *
     * @param dutySchedule 值班安排
     * @return 结果
     */
    int updateDutySchedule(EmDutyScheduleDTO dutySchedule);

    /**
     * 修改值班安排（包含人员信息）
     *
     * @param dutyScheduleDetail 值班安排详情（包含人员信息）
     * @return 结果
     */
    int updateDutyScheduleWithPersons(EmDutyScheduleDetailVO dutyScheduleDetail);
    
    /**
     * 批量删除值班安排
     * 
     * @param ids 需要删除的值班安排主键集合
     * @return 结果
     */
    int deleteDutyScheduleByIds(String[] ids);
    
    /**
     * 查询指定日期的值班安排
     * 
     * @param dutyDate 值班日期
     * @return 值班安排列表
     */
    List<EmDutyScheduleDetailVO> selectDutyScheduleByDate(String dutyDate);
    
    /**
     * 查询当前值班人员
     *
     * @param dutySchedule 筛选条件
     * @return 当前值班人员列表
     */
    List<EmDutyPersonVO> selectCurrentDutyPersons(EmDutyScheduleDTO dutySchedule);

    /**
     * 查询今日所有值班人员（调试用）
     *
     * @return 今日值班人员列表
     */
    List<EmDutyPersonVO> selectTodayDutyPersons();
    
    // ========== 值班人员管理（用于后续调整） ==========
    
    /**
     * 添加值班人员
     * 
     * @param dutyPerson 值班人员
     * @return 结果
     */
    int addDutyPerson(EmDutyPersonDTO dutyPerson);
    
    /**
     * 批量添加值班人员
     * 
     * @param dutyPersons 值班人员列表
     * @return 结果
     */
    int addDutyPersonBatch(List<EmDutyPersonDTO> dutyPersons);
    
    /**
     * 修改值班人员
     * 
     * @param dutyPerson 值班人员
     * @return 结果
     */
    int updateDutyPerson(EmDutyPersonDTO dutyPerson);
    
    /**
     * 删除值班人员
     * 
     * @param ids 需要删除的值班人员主键集合
     * @return 结果
     */
    int deleteDutyPersonByIds(String[] ids);
    
    /**
     * 查询个人值班安排
     * 
     * @param dutyPerson 查询条件
     * @return 个人值班安排列表
     */
    List<EmDutyPersonVO> selectMyDutySchedule(EmDutyPersonDTO dutyPerson);
}
