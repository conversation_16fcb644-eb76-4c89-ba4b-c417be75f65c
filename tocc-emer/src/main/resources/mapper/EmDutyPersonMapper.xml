<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.duty.mapper.EmDutyPersonMapper">
    
    <resultMap type="com.tocc.duty.domain.vo.EmDutyPersonVO" id="EmDutyPersonVOResult">
        <result property="id"                    column="id"                    />
        <result property="scheduleId"            column="schedule_id"           />
        <result property="scheduleName"          column="schedule_name"         />
        <result property="dutyDate"              column="duty_date"             />
        <result property="shiftType"             column="shift_type"            />
        <result property="shiftTypeName"         column="shift_type_name"       />
        <result property="startTime"             column="start_time"            />
        <result property="endTime"               column="end_time"              />
        <result property="personName"            column="person_name"           />
        <result property="personPosition"        column="person_position"       />
        <result property="personUnit"            column="person_unit"           />
        <result property="personType"            column="person_type"           />
        <result property="personTypeName"        column="person_type_name"      />
        <result property="contactPhone"          column="contact_phone"         />
        <result property="dutyLocation"          column="duty_location"         />
        <result property="createTime"            column="create_time"           />
        <result property="remark"                column="remark"                />
    </resultMap>
    
    <sql id="selectDutyPersonVo">
        select dp.id, dp.schedule_id, ds.schedule_name, dp.duty_date,
               dp.shift_type,
               sdt1.dict_label as shift_type_name,
               case dp.shift_type 
                   when '1' then concat(ds.day_shift_start_time, '-', ds.day_shift_end_time)
                   when '2' then concat(ds.night_shift_start_time, '-', ds.night_shift_end_time)
                   when '3' then concat(ds.full_shift_start_time, '-', ds.full_shift_end_time)
                   else ''
               end as start_time,
               '' as end_time,
               dp.person_name, dp.person_position, dp.person_unit,
               dp.person_type,
               sdt2.dict_label as person_type_name,
               dp.contact_phone, '' as duty_location,
               dp.create_time, dp.remark
        from em_duty_person dp
        inner join em_duty_schedule ds on dp.schedule_id = ds.id
        left join sys_dict_data sdt1 on sdt1.dict_type = 'shift_type' and sdt1.dict_value = dp.shift_type and sdt1.status = '0'
        left join sys_dict_data sdt2 on sdt2.dict_type = 'duty_person_type' and sdt2.dict_value = dp.person_type and sdt2.status = '0'
        where dp.del_flag = 0 and ds.del_flag = 0
    </sql>
    
    <select id="selectDutyPersonList" parameterType="com.tocc.duty.domain.dto.EmDutyPersonDTO" resultMap="EmDutyPersonVOResult">
        <include refid="selectDutyPersonVo"/>
        <where>
            <if test="scheduleId != null and scheduleId != ''">
                and dp.schedule_id = #{scheduleId}
            </if>
            <if test="dutyDate != null">
                and dp.duty_date = #{dutyDate}
            </if>
            <if test="shiftType != null and shiftType != ''">
                and dp.shift_type = #{shiftType}
            </if>
            <if test="personName != null and personName != ''">
                and dp.person_name like concat('%', #{personName}, '%')
            </if>
            <if test="personType != null and personType != ''">
                and dp.person_type = #{personType}
            </if>
            <if test="scheduleName != null and scheduleName != ''">
                and ds.schedule_name like concat('%', #{scheduleName}, '%')
            </if>
        </where>
        order by dp.duty_date desc, dp.person_type, dp.create_time
    </select>
    
    <select id="selectDutyPersonByScheduleId" parameterType="String" resultMap="EmDutyPersonVOResult">
        <include refid="selectDutyPersonVo"/>
        and dp.schedule_id = #{scheduleId}
        order by dp.duty_date, dp.person_type, dp.create_time
    </select>
    
    <select id="selectMyDutySchedule" parameterType="com.tocc.duty.domain.dto.EmDutyPersonDTO" resultMap="EmDutyPersonVOResult">
        <include refid="selectDutyPersonVo"/>
        <where>
            <if test="personName != null and personName != ''">
                and dp.person_name = #{personName}
            </if>
            <if test="dutyDate != null">
                and dp.duty_date &gt;= #{dutyDate}
            </if>
            <if test="shiftType != null and shiftType != ''">
                and dp.shift_type = #{shiftType}
            </if>
            <if test="personType != null and personType != ''">
                and dp.person_type = #{personType}
            </if>
        </where>
        order by dp.duty_date desc, dp.create_time desc
    </select>
    
    <insert id="insertBatch" parameterType="java.util.List">
        insert into em_duty_person(
            id, schedule_id, duty_date, shift_type, person_name, person_position, 
            person_unit, person_type, contact_phone, del_flag, create_time, 
            creator, remark
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.scheduleId}, #{item.dutyDate}, #{item.shiftType}, 
                #{item.personName}, #{item.personPosition}, #{item.personUnit}, 
                #{item.personType}, #{item.contactPhone}, 0, now(), 
                #{item.createBy}, #{item.remark}
            )
        </foreach>
    </insert>
    
</mapper>
