<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.duty.mapper.EmDutyScheduleMapper">
    
    <resultMap type="com.tocc.duty.domain.vo.EmDutyScheduleVO" id="EmDutyScheduleVOResult">
        <result property="id"                    column="id"                    />
        <result property="scheduleName"          column="schedule_name"         />
        <result property="startDate"             column="start_date"            />
        <result property="endDate"               column="end_date"              />
        <result property="creatorName"           column="creator_name"          />
        <result property="creatorDeptName"       column="creator_dept_name"     />
        <result property="dayShiftTime"          column="day_shift_time"        />
        <result property="nightShiftTime"        column="night_shift_time"      />
        <result property="fullShiftTime"         column="full_shift_time"       />

        <result property="contactPhone"          column="contact_phone"         />
        <result property="status"                column="status"                />
        <result property="statusName"            column="status_name"           />
        <result property="totalDays"             column="total_days"            />
        <result property="personCount"           column="person_count"          />
        <result property="createTime"            column="create_time"           />
        <result property="remark"                column="remark"                />
    </resultMap>
    
    <resultMap type="com.tocc.duty.domain.vo.EmDutyScheduleDetailVO" id="EmDutyScheduleDetailVOResult">
        <result property="id"                    column="id"                    />
        <result property="scheduleName"          column="schedule_name"         />
        <result property="startDate"             column="start_date"            />
        <result property="endDate"               column="end_date"              />
        <result property="creatorName"           column="creator_name"          />
        <result property="creatorDeptName"       column="creator_dept_name"     />
        <result property="dayShiftStartTime"     column="day_shift_start_time"  />
        <result property="dayShiftEndTime"       column="day_shift_end_time"    />
        <result property="nightShiftStartTime"   column="night_shift_start_time"/>
        <result property="nightShiftEndTime"     column="night_shift_end_time"  />
        <result property="fullShiftStartTime"    column="full_shift_start_time" />
        <result property="fullShiftEndTime"      column="full_shift_end_time"   />

        <result property="contactPhone"          column="contact_phone"         />
        <result property="dutyRequirements"      column="duty_requirements"     />
        <result property="dutyResponsibilities"  column="duty_responsibilities" />
        <result property="status"                column="status"                />
        <result property="statusName"            column="status_name"           />
        <result property="totalDays"             column="total_days"            />
        <result property="createTime"            column="create_time"           />
        <result property="remark"                column="remark"                />
    </resultMap>
    
    <resultMap type="com.tocc.duty.domain.vo.EmDutyPersonVO" id="EmDutyPersonVOResult">
        <result property="id"                    column="id"                    />
        <result property="scheduleId"            column="schedule_id"           />
        <result property="scheduleName"          column="schedule_name"         />
        <result property="dutyDate"              column="duty_date"             />
        <result property="shiftType"             column="shift_type"            />
        <result property="shiftTypeName"         column="shift_type_name"       />
        <result property="startTime"             column="start_time"            />
        <result property="endTime"               column="end_time"              />
        <result property="personName"            column="person_name"           />
        <result property="personPosition"        column="person_position"       />
        <result property="personUnit"            column="person_unit"           />
        <result property="personType"            column="person_type"           />
        <result property="personTypeName"        column="person_type_name"      />
        <result property="contactPhone"          column="contact_phone"         />

        <result property="createTime"            column="create_time"           />
        <result property="remark"                column="remark"                />
    </resultMap>
    
    <sql id="selectDutyScheduleVo">
        select ds.id, ds.schedule_name, ds.start_date, ds.end_date,
               u.user_name as creator_name,
               d.dept_name as creator_dept_name,
               concat(
                   case when ds.day_shift_start_time is not null 
                        then concat('白班:', ds.day_shift_start_time, '-', ds.day_shift_end_time) 
                        else '' end,
                   case when ds.night_shift_start_time is not null 
                        then concat(case when ds.day_shift_start_time is not null then ' ' else '' end,
                                   '夜班:', ds.night_shift_start_time, '-', ds.night_shift_end_time) 
                        else '' end,
                   case when ds.full_shift_start_time is not null 
                        then concat(case when ds.day_shift_start_time is not null or ds.night_shift_start_time is not null then ' ' else '' end,
                                   '全天:', ds.full_shift_start_time, '-', ds.full_shift_end_time) 
                        else '' end
               ) as day_shift_time,
               '' as night_shift_time,
               '' as full_shift_time,
               ds.contact_phone, ds.status,
               case ds.status when '0' then '正常' when '1' then '取消' else '未知' end as status_name,
               datediff(ds.end_date, ds.start_date) + 1 as total_days,
               (select count(*) from em_duty_person dp where dp.schedule_id = ds.id and dp.del_flag = 0) as person_count,
               ds.create_time, ds.remark
        from em_duty_schedule ds
        left join sys_user u on ds.creator_user_id = u.user_id
        left join sys_dept d on ds.creator_dept_id = d.dept_id
        where ds.del_flag = 0
    </sql>
    
    <select id="selectDutyScheduleList" parameterType="com.tocc.duty.domain.dto.EmDutyScheduleDTO" resultMap="EmDutyScheduleVOResult">
        <include refid="selectDutyScheduleVo"/>
        <where>
            <if test="scheduleName != null and scheduleName != ''">
                and ds.schedule_name like concat('%', #{scheduleName}, '%')
            </if>
            <if test="startDate != null">
                and ds.start_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                and ds.end_date &lt;= #{endDate}
            </if>
            <if test="status != null and status != ''">
                and ds.status = #{status}
            </if>
            <if test="creatorName != null and creatorName != ''">
                and u.user_name like concat('%', #{creatorName}, '%')
            </if>
            <if test="creatorDeptName != null and creatorDeptName != ''">
                and d.dept_name like concat('%', #{creatorDeptName}, '%')
            </if>
            ${params.dataScope}
        </where>
        order by ds.create_time desc
    </select>
    
    <select id="selectDutyScheduleDetailById" parameterType="String" resultMap="EmDutyScheduleDetailVOResult">
        select ds.id, ds.schedule_name, ds.start_date, ds.end_date,
               u.user_name as creator_name,
               d.dept_name as creator_dept_name,
               ds.day_shift_start_time, ds.day_shift_end_time,
               ds.night_shift_start_time, ds.night_shift_end_time,
               ds.full_shift_start_time, ds.full_shift_end_time,
               ds.contact_phone,
               ds.duty_requirements, ds.duty_responsibilities,
               ds.status,
               case ds.status when '0' then '正常' when '1' then '取消' else '未知' end as status_name,
               datediff(ds.end_date, ds.start_date) + 1 as total_days,
               ds.create_time, ds.remark
        from em_duty_schedule ds
        left join sys_user u on ds.creator_user_id = u.user_id
        left join sys_dept d on ds.creator_dept_id = d.dept_id
        where ds.id = #{id} and ds.del_flag = 0
    </select>
    
    <select id="selectDutyScheduleByDate" parameterType="String" resultMap="EmDutyScheduleDetailVOResult">
        select ds.id, ds.schedule_name, ds.start_date, ds.end_date,
               u.user_name as creator_name,
               d.dept_name as creator_dept_name,
               ds.day_shift_start_time, ds.day_shift_end_time,
               ds.night_shift_start_time, ds.night_shift_end_time,
               ds.full_shift_start_time, ds.full_shift_end_time,
               ds.contact_phone,
               ds.duty_requirements, ds.duty_responsibilities,
               ds.status,
               case ds.status when '0' then '正常' when '1' then '取消' else '未知' end as status_name,
               datediff(ds.end_date, ds.start_date) + 1 as total_days,
               ds.create_time, ds.remark
        from em_duty_schedule ds
        left join sys_user u on ds.creator_user_id = u.user_id
        left join sys_dept d on ds.creator_dept_id = d.dept_id
        where ds.del_flag = 0 
          and ds.status = '0'
          and #{dutyDate} between ds.start_date and ds.end_date
        order by ds.create_time desc
    </select>
    
    <select id="selectCurrentDutyPersons" resultMap="EmDutyPersonVOResult">
        select dp.id, dp.schedule_id, ds.schedule_name, dp.duty_date,
               dp.shift_type,
               sdt1.dict_label as shift_type_name,
               case dp.shift_type 
                   when '1' then concat(ds.day_shift_start_time, '-', ds.day_shift_end_time)
                   when '2' then concat(ds.night_shift_start_time, '-', ds.night_shift_end_time)
                   when '3' then concat(ds.full_shift_start_time, '-', ds.full_shift_end_time)
                   else ''
               end as start_time,
               '' as end_time,
               dp.person_name, dp.person_position, dp.person_unit,
               dp.person_type,
               sdt2.dict_label as person_type_name,
               dp.contact_phone, '' as duty_location,
               dp.create_time, dp.remark
        from em_duty_person dp
        inner join em_duty_schedule ds on dp.schedule_id = ds.id
        left join sys_dict_data sdt1 on sdt1.dict_type = 'shift_type' and sdt1.dict_value = dp.shift_type and sdt1.status = '0'
        left join sys_dict_data sdt2 on sdt2.dict_type = 'duty_person_type' and sdt2.dict_value = dp.person_type and sdt2.status = '0'
        where dp.del_flag = 0 
          and ds.del_flag = 0 
          and ds.status = '0'
          and dp.duty_date = curdate()
          and (
              (dp.shift_type = '1' and curtime() between ds.day_shift_start_time and ds.day_shift_end_time) or
              (dp.shift_type = '2' and curtime() between ds.night_shift_start_time and ds.night_shift_end_time) or
              (dp.shift_type = '3' and curtime() between ds.full_shift_start_time and ds.full_shift_end_time)
          )
        order by dp.person_type, dp.create_time
    </select>
    
</mapper>
